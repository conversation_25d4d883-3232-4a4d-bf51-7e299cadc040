'use client'

import dayjs from 'dayjs'
import { TimePicker } from '@/components/ui/time-picker'
import { RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'

export default function TimeField({
    time = dayjs().format('HH:mm'),
    handleTimeChange,
    timeID,
    fieldName = 'Time',
    buttonLabel = 'Set To Now',
    hideButton = false,
    disabled,
}: {
    time: any
    handleTimeChange: any
    timeID: any
    fieldName: any
    buttonLabel?: any
    hideButton?: any
    disabled?: boolean
}) {
    interface Option {
        label: any
        value: any
    }

    let currentDate = new Date()
    let nextWeek: Option[] = []

    for (let i = 0; i < 7; i++) {
        let date = new Date()
        date.setDate(currentDate.getDate() + i)
        let setDay = dayjs(date).format('YYYY-MM-DD')
        nextWeek = nextWeek.concat({ label: setDay, value: setDay })
    }

    return (
        <TimePicker
            disabled={disabled}
            value={
                time &&
                dayjs(`${dayjs().format('YYYY-MM-DD')} ${time}`).isValid()
                    ? dayjs(`${dayjs().format('YYYY-MM-DD')} ${time}`).toDate()
                    : new Date()
            }
            onChange={handleTimeChange}
            use24Hour={true}
            className="tablet-sm:w-[300px] w-full"
            nowButton={!hideButton}
            nowButtonLabel={buttonLabel}
        />
    )
}
