'use client'
import React, { useState } from 'react'
import dayjs from 'dayjs'

import { TimePicker } from '@/components/ui/time-picker'

export default function RadioTimeField({
    log,
    handleTimeChange,
    buttonLabel = 'Set To Now',
    hideButton = false,
}: {
    log: any
    handleTimeChange: any
    buttonLabel?: any
    hideButton?: any
}) {
    const [time, setTime] = useState<any>(log.time ? dayjs(log.time) : false)
    const handleTimeFieldChange = (time: any) => {
        setTime(time)
        handleTimeChange(log, time)
    }

    return (
        <TimePicker
            value={time ? dayjs(time).toDate() : new Date()}
            onChange={(newTime) => {
                handleTimeFieldChange(newTime)
            }}
            use24Hour={true}
            className="w-full md:max-w-80"
            nowButton={!hideButton}
            nowButtonLabel={buttonLabel}
        />
    )
}
