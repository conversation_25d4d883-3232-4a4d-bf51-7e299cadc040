'use client'
import React, { useEffect, useState } from 'react'
import { useLazyQuery } from '@apollo/client'
import {
    GET_CREW_BY_IDS,
    GET_MAINTENANCE_CHECK_LIST,
} from '@/app/lib/graphQL/query'
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { List } from '@/components/skeletons'
import Link from 'next/link'

import { getVesselList } from '@/app/lib/actions'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash'
import { usePathname, useSearchParams } from 'next/navigation'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { dueStatusLabel } from '../reporting/maintenance-status-activity-report'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import { DataTable, createColumns } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { MaintenanceFilterActions } from '@/components/filter/components/maintenance-actions'
import { ListHeader } from '@/components/ui/list-header'

// Status badge component following UI standards
export const StatusBadge = ({ maintenanceCheck }: { maintenanceCheck: any }) => {
    const isOverdue = maintenanceCheck?.isOverDue?.status === 'High'

    // Get status text
    let statusText = ''
    if (
        maintenanceCheck?.isOverDue?.status &&
        ['High', 'Medium', 'Low'].includes(maintenanceCheck.isOverDue.status)
    ) {
        statusText = maintenanceCheck?.isOverDue?.days
    } else if (
        maintenanceCheck?.isOverDue?.status === 'Completed' &&
        maintenanceCheck?.isOverDue?.days === 'Save As Draft'
    ) {
        statusText = maintenanceCheck?.isOverDue?.days
    } else if (maintenanceCheck?.isOverDue?.status === 'Upcoming') {
        statusText = maintenanceCheck?.isOverDue?.days
    } else if (
        maintenanceCheck?.isOverDue?.status === 'Completed' &&
        isEmpty(maintenanceCheck?.isOverDue?.days)
    ) {
        statusText = maintenanceCheck?.isOverDue?.status
    } else if (
        maintenanceCheck?.isOverDue?.status === 'Completed' &&
        !isEmpty(maintenanceCheck?.isOverDue?.days) &&
        maintenanceCheck?.isOverDue?.days !== 'Save As Draft'
    ) {
        statusText = maintenanceCheck?.isOverDue?.days
    }

    // Only apply styling to overdue items, others are plain text
    if (isOverdue) {
        return (
            <span className="alert inline-block rounded-md px-3 py-1">
                {statusText}
            </span>
        )
    }

    return <span>{statusText}</span>
}

// Reusable MaintenanceTable component that accepts props
export function MaintenanceTable({
    maintenanceChecks,
    vessels,
    crewInfo,
    showVessel = false,
}: {
    maintenanceChecks: any[]
    vessels: any[]
    crewInfo: any[]
    showVessel?: boolean
}) {
    const pathname = usePathname()
    const searchParams = useSearchParams()

    const columns = createColumns([
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Title" />
            ),
            cell: ({ row }: { row: any }) => {
                const maintenanceCheck: any = row.original
                return (
                    <div className="space-y-2">
                        <div className="flex items-center gap-2">
                            <div className="hidden md:block">
                                {(maintenanceCheck.severity === 'High' ||
                                    maintenanceCheck.severity === 'Medium') && (
                                    <ExclamationTriangleIcon
                                        className={`h-5 w-5 ${
                                            maintenanceCheck.severity === 'High'
                                                ? 'text-destructive'
                                                : 'text-slorange-1000'
                                        }`}
                                    />
                                )}
                            </div>
                            <Link
                                href={`/maintenance?taskID=${maintenanceCheck.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                                className="hover:underline font-medium">
                                <div className="flex items-center gap-2 md:hidden">
                                    {(maintenanceCheck.severity === 'High' ||
                                        maintenanceCheck.severity ===
                                            'Medium') && (
                                        <ExclamationTriangleIcon
                                            className={`h-5 w-5 ${
                                                maintenanceCheck.severity ===
                                                'High'
                                                    ? 'text-destructive'
                                                    : 'text-slorange-1000'
                                            }`}
                                        />
                                    )}
                                </div>
                                {maintenanceCheck.name ??
                                    `Task #${maintenanceCheck.id} (No Name) - ${dayjs(
                                        maintenanceCheck.created,
                                    ).format('DD/MM/YYYY')}`}
                            </Link>
                        </div>

                        {/* Mobile: Show location */}
                        {!showVessel && (
                            <div className="lg:hidden">
                                {maintenanceCheck.basicComponent?.id > 0 && (
                                    <Link
                                        href={`/vessel/info?id=${maintenanceCheck.basicComponent.id}`}
                                        className="text-sm text-muted-foreground hover:underline">
                                        {maintenanceCheck.basicComponent.title}
                                    </Link>
                                )}
                            </div>
                        )}

                        {/* Mobile: Show assigned to and status */}
                        <div className="md:hidden space-y-2">
                            {maintenanceCheck.assignedTo?.id > 0 && (
                                <div className="text-sm">
                                    <span className="text-muted-foreground">
                                        Assigned to:{' '}
                                    </span>
                                    <Link
                                        href={`/crew/info?id=${maintenanceCheck.assignedTo.id}`}
                                        className="hover:underline">
                                        {maintenanceCheck.assignedTo.name}
                                    </Link>
                                </div>
                            )}
                            <div>
                                <StatusBadge
                                    maintenanceCheck={maintenanceCheck}
                                />
                            </div>
                        </div>
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.name || ''
                const valueB = rowB?.original?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        ...(showVessel
            ? []
            : [
                  {
                      accessorKey: 'location',
                      header: ({ column }: { column: any }) => (
                          <DataTableSortHeader
                              column={column}
                              title="Location"
                          />
                      ),
                      cellAlignment: 'left' as const,
                      cell: ({ row }: { row: any }) => {
                          const maintenanceCheck = row.original
                          return (
                              <div className="hidden lg:block">
                                  {maintenanceCheck.basicComponent?.id > 0 && (
                                      <Link
                                          href={`/vessel/info?id=${maintenanceCheck.basicComponent.id}`}
                                          className="hover:underline">
                                          {
                                              maintenanceCheck.basicComponent
                                                  .title
                                          }
                                      </Link>
                                  )}
                              </div>
                          )
                      },
                      sortingFn: (rowA: any, rowB: any) => {
                          const valueA =
                              rowA?.original?.basicComponent?.title || ''
                          const valueB =
                              rowB?.original?.basicComponent?.title || ''
                          return valueA.localeCompare(valueB)
                      },
                  },
              ]),
        {
            accessorKey: 'assigned',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Assigned to" />
            ),
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const maintenanceCheck = row.original
                return (
                    <div className="hidden md:block">
                        {maintenanceCheck.assignedTo?.id > 0 && (
                            <Link
                                href={`/crew/info?id=${maintenanceCheck.assignedTo.id}`}
                                className="hover:underline">
                                {maintenanceCheck.assignedTo.name}
                            </Link>
                        )}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.assignedTo?.name || ''
                const valueB = rowB?.original?.assignedTo?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'inventory',
            header: 'Inventory item',
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const maintenanceCheck = row.original
                return (
                    <div className="hidden md:block">
                        {maintenanceCheck.inventory?.id > 0 && (
                            <Link
                                href={`/inventory/view?id=${maintenanceCheck.inventory.id}`}
                                className="hover:underline">
                                {maintenanceCheck.inventory.item}
                            </Link>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'status',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Status" />
            ),
            cellAlignment: 'right' as const,
            cell: ({ row }: { row: any }) => {
                const maintenanceCheck = row.original
                return (
                    <div className="hidden md:block">
                        <StatusBadge maintenanceCheck={maintenanceCheck} />
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.isOverDue?.days || ''
                const valueB = rowB?.original?.isOverDue?.days || ''
                return valueA.localeCompare(valueB)
            },
        },
    ])

    return (
        <DataTable
            columns={columns}
            data={maintenanceChecks}
            pageSize={20}
            showToolbar={false}
        />
    )
}

export default function TaskList() {
    const [maintenanceChecks, setMaintenanceChecks] = useState<any>()
    const [filteredMaintenanceChecks, setFilteredMaintenanceChecks] =
        useState<any>()
    const [vessels, setVessels] = useState<any>()
    const [crewInfo, setCrewInfo] = useState<any>()
    const [filter, setFilter] = useState({} as SearchFilter)
    const [isLoading, setIsLoading] = useState(true)
    const [keywordFilter, setKeywordFilter] = useState([] as any)
    const [permissions, setPermissions] = useState<any>(false)
    const [edit_task, setEdit_task] = useState<any>(false)
    const pathname = usePathname()
    const searchParams = useSearchParams()

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_TASK', permissions)) {
                setEdit_task(true)
            } else {
                setEdit_task(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        if (permissions) {
            init_permissions()
        }
    }, [permissions])

    const [queryMaintenanceChecks] = useLazyQuery(GET_MAINTENANCE_CHECK_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readComponentMaintenanceCheckList[0].list
            if (data) {
                handleSetMaintenanceChecks(data)
            }
        },
        onError: (error: any) => {
            console.error('queryMaintenanceChecks error', error)
        },
    })
    useEffect(() => {
        if (isLoading) {
            loadMaintenanceChecks()
            setIsLoading(false)
        }
    }, [isLoading])
    const loadMaintenanceChecks = async () => {
        await queryMaintenanceChecks({
            variables: {
                inventoryID: 0,
                vesselID: 0,
            },
        })
    }
    const handleSetVessels = (vessels: any) => {
        const activeVessels = vessels.filter((vessel: any) => !vessel.archived)
        const appendedData = activeVessels.map((item: any) => ({
            ...item,
        }))
        appendedData.push({ title: 'Other', id: 0 })
        setVessels(appendedData)
    }

    getVesselList(handleSetVessels)

    const handleSetMaintenanceChecks = (tasks: any) => {
        setMaintenanceChecks(tasks)
        setFilteredMaintenanceChecks(tasks)
        const appendedData: number[] = Array.from(
            new Set(
                tasks
                    .filter((item: any) => item.assignedTo.id > 0)
                    .map((item: any) => item.assignedTo.id),
            ),
        )
        loadCrewMemberInfo(appendedData)
    }

    const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readSeaLogsMembers.nodes
            if (data) {
                setCrewInfo(data)
            }
        },
        onError: (error) => {
            console.error('queryCrewMemberInfo error', error)
        },
    })
    const loadCrewMemberInfo = async (crewId: any) => {
        await queryCrewMemberInfo({
            variables: {
                crewMemberIDs: crewId.length > 0 ? crewId : [0],
            },
        })
    }
    const handleFilterOnChange = ({ type, data }: any) => {
        const searchFilter: SearchFilter = { ...filter }
        var filteredTasks = maintenanceChecks

        // Vessel filter
        if (type === 'vessel') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.basicComponentID = {
                    in: data.map((item) => +item.value),
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.basicComponentID = { eq: +data.value }
            } else {
                delete searchFilter.basicComponentID
            }
        }

        // Status filter
        if (type === 'status') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.status = { in: data.map((item) => item.value) }
            } else if (data && !Array.isArray(data)) {
                searchFilter.status = { eq: data.value }
            } else {
                delete searchFilter.status
            }
        }

        // Assigned member filter
        if (type === 'member') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.assignedToID = {
                    in: data.map((item) => +item.value),
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.assignedToID = { eq: +data.value }
            } else {
                delete searchFilter.assignedToID
            }
        }

        // Date range
        if (type === 'dateRange') {
            if (data?.startDate && data?.endDate) {
                searchFilter.expires = {
                    gte: data.startDate,
                    lte: data.endDate,
                }
            } else {
                delete searchFilter.expires
            }
        }

        // Category
        if (type === 'category') {
            if (Array.isArray(data) && data.length > 0) {
                searchFilter.maintenanceCategoryID = {
                    in: data.map((item) => +item.value),
                }
            } else if (data && !Array.isArray(data)) {
                searchFilter.maintenanceCategoryID = { eq: +data.value }
            } else {
                delete searchFilter.maintenanceCategoryID
            }
        }

        // Keyword filter
        let keyFilter = keywordFilter
        if (type === 'keyword' || keyFilter?.length > 0) {
            const keyword = data?.value?.trim().toLowerCase()
            if (keyword && keyword.length > 0) {
                filteredTasks = filteredTasks.filter((maintenanceCheck: any) =>
                    [
                        maintenanceCheck.name,
                        maintenanceCheck.comments,
                        maintenanceCheck.workOrderNumber,
                    ].some((field) => field?.toLowerCase().includes(keyword)),
                )
                keyFilter = data.value
            } else {
                keyFilter = null
            }
        }

        // Filtering based on current searchFilter

        // Filter by vessel (basicComponentID)
        if (searchFilter.basicComponentID) {
            const ids = searchFilter.basicComponentID.in || [
                searchFilter.basicComponentID.eq,
            ]
            filteredTasks = filteredTasks.filter((mc: any) =>
                ids.includes(mc.basicComponent?.id),
            )
        }

        // Filter by status
        if (searchFilter.status) {
            const statuses = searchFilter.status.in || [searchFilter.status.eq]
            filteredTasks = filteredTasks.filter((mc: any) =>
                statuses.includes(mc.status),
            )
        }

        // Filter by assignedToID
        if (searchFilter.assignedToID) {
            const ids = searchFilter.assignedToID.in || [
                searchFilter.assignedToID.eq,
            ]
            filteredTasks = filteredTasks.filter((mc: any) =>
                ids.includes(mc.assignedTo?.id),
            )
        }

        // Filter by category
        if (searchFilter.maintenanceCategoryID) {
            const ids = searchFilter.maintenanceCategoryID.in || [
                searchFilter.maintenanceCategoryID.eq,
            ]
            filteredTasks = filteredTasks.filter((mc: any) =>
                ids.includes(mc.maintenanceCategoryID),
            )
        }

        // Filter by date range
        if (searchFilter.expires) {
            filteredTasks = filteredTasks.filter(
                (mc: any) =>
                    dayjs(mc.startDate).isAfter(
                        dayjs(searchFilter.expires.gte),
                    ) &&
                    dayjs(mc.startDate).isBefore(
                        dayjs(searchFilter.expires.lte),
                    ),
            )
        }

        // Set updated filters
        setFilter(searchFilter)
        setKeywordFilter(keyFilter)
        setFilteredMaintenanceChecks(filteredTasks)
        // Optionally call API: loadMaintenanceChecks(searchFilter, keyFilter);
    }

    const downloadCsv = () => {
        if ((maintenanceChecks && vessels) === false) {
            return
        }

        const csvEntries: string[][] = [
            ['task', 'location', 'assigned to', 'due'],
        ]

        maintenanceChecks
            .filter(
                (maintenanceCheck: any) =>
                    maintenanceCheck.status !== 'Save_As_Draft',
            )
            .forEach((maintenanceCheck: any) => {
                csvEntries.push([
                    maintenanceCheck.name,
                    vessels
                        ?.filter(
                            (vessel: any) =>
                                vessel?.id == maintenanceCheck.basicComponentID,
                        )
                        .map((vessel: any) => vessel.title)
                        .join(', '),
                    crewInfo
                        .filter(
                            (crew: any) =>
                                crew.id === maintenanceCheck.assignedToID,
                        )
                        .map((crew: any, index: number) => {
                            return `${crew.firstName} ${crew.surname}`
                        })
                        .join(', '),
                    dueStatusLabel(maintenanceCheck.isOverDue),
                ])
            })

        exportCsv(csvEntries)
    }

    const downloadPdf = () => {
        if ((maintenanceChecks && vessels) === false) {
            return
        }

        const headers: any = [['Task Name', 'Location', 'Assigned To', 'Due']]

        const body: any = maintenanceChecks
            .filter(
                (maintenanceCheck: any) =>
                    maintenanceCheck.status !== 'Save_As_Draft',
            )
            .map((maintenanceCheck: any) => {
                return [
                    maintenanceCheck.name,
                    vessels
                        ?.filter(
                            (vessel: any) =>
                                vessel?.id == maintenanceCheck.basicComponentID,
                        )
                        .map((vessel: any) => vessel.title)
                        .join(', '),
                    crewInfo
                        .filter(
                            (crew: any) =>
                                crew.id === maintenanceCheck.assignedToID,
                        )
                        .map((crew: any, index: number) => {
                            return `${crew.firstName} ${crew.surname}`
                        })
                        .join(', '),
                    dueStatusLabel(maintenanceCheck.isOverDue),
                ]
            })

        exportPdfTable({
            headers,
            body,
        })
    }

    const columns = createColumns([
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Title" />
            ),
            cell: ({ row }: { row: any }) => {
                const maintenanceCheck: any = row.original
                return (
                    <div className="space-y-2">
                        <div className="flex items-center gap-2">
                            <div className="hidden md:block">
                                {(maintenanceCheck.severity === 'High' ||
                                    maintenanceCheck.severity === 'Medium') && (
                                    <ExclamationTriangleIcon
                                        className={`h-5 w-5 ${
                                            maintenanceCheck.severity === 'High'
                                                ? 'text-destructive'
                                                : 'text-slorange-1000'
                                        }`}
                                    />
                                )}
                            </div>
                            <Link
                                href={`/maintenance?taskID=${maintenanceCheck.id}&redirect_to=${pathname}?${searchParams.toString()}`}
                                className="hover:underline font-medium">
                                <div className="flex items-center gap-2 md:hidden">
                                    {(maintenanceCheck.severity === 'High' ||
                                        maintenanceCheck.severity ===
                                            'Medium') && (
                                        <ExclamationTriangleIcon
                                            className={`h-5 w-5 ${
                                                maintenanceCheck.severity ===
                                                'High'
                                                    ? 'text-destructive'
                                                    : 'text-slorange-1000'
                                            }`}
                                        />
                                    )}
                                </div>
                                {maintenanceCheck.name ??
                                    `Task #${maintenanceCheck.id} (No Name) - ${dayjs(
                                        maintenanceCheck.created,
                                    ).format('DD/MM/YYYY')}`}
                            </Link>
                        </div>

                        {/* Mobile: Show location */}
                        <div className="lg:hidden">
                            {maintenanceCheck.basicComponent?.id > 0 && (
                                <Link
                                    href={`/vessel/info?id=${maintenanceCheck.basicComponent.id}`}
                                    className="text-sm text-muted-foreground hover:underline">
                                    {maintenanceCheck.basicComponent.title}
                                </Link>
                            )}
                        </div>

                        {/* Mobile: Show assigned to and status */}
                        <div className="md:hidden space-y-2">
                            {maintenanceCheck.assignedTo?.id > 0 && (
                                <div className="text-sm">
                                    <span className="text-muted-foreground">
                                        Assigned to:{' '}
                                    </span>
                                    <Link
                                        href={`/crew/info?id=${maintenanceCheck.assignedTo.id}`}
                                        className="hover:underline">
                                        {maintenanceCheck.assignedTo.name}
                                    </Link>
                                </div>
                            )}
                            <div>
                                <StatusBadge
                                    maintenanceCheck={maintenanceCheck}
                                />
                            </div>
                        </div>
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.name || ''
                const valueB = rowB?.original?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'location',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Location" />
            ),
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const maintenanceCheck = row.original
                return (
                    <div className="hidden lg:block">
                        {maintenanceCheck.basicComponent?.id > 0 && (
                            <Link
                                href={`/vessel/info?id=${maintenanceCheck.basicComponent.id}`}
                                className="hover:underline">
                                {maintenanceCheck.basicComponent.title}
                            </Link>
                        )}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.basicComponent?.title || ''
                const valueB = rowB?.original?.basicComponent?.title || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'assigned',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Assigned to" />
            ),
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const maintenanceCheck = row.original
                return (
                    <div className="hidden md:block">
                        {maintenanceCheck.assignedTo?.id > 0 && (
                            <Link
                                href={`/crew/info?id=${maintenanceCheck.assignedTo.id}`}
                                className="hover:underline">
                                {maintenanceCheck.assignedTo.name}
                            </Link>
                        )}
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.assignedTo?.name || ''
                const valueB = rowB?.original?.assignedTo?.name || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'inventory',
            header: 'Inventory item',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const maintenanceCheck = row.original
                return (
                    <div className="hidden md:block">
                        {maintenanceCheck.inventory?.id > 0 && (
                            <Link
                                href={`/inventory/view?id=${maintenanceCheck.inventory.id}`}
                                className="hover:underline">
                                {maintenanceCheck.inventory.item}
                            </Link>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'status',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Status" />
            ),
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const maintenanceCheck = row.original
                return (
                    <div className="hidden md:block">
                        <StatusBadge maintenanceCheck={maintenanceCheck} />
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.isOverDue?.days || ''
                const valueB = rowB?.original?.isOverDue?.days || ''
                return valueA.localeCompare(valueB)
            },
        },
    ])

    return (
        <>
            <ListHeader
                title="Maintenance"
                icon={<></>}
                actions={<MaintenanceFilterActions />}
                titleClassName=""
            />
            <div className="mt-16">
                {maintenanceChecks && vessels ? (
                    <DataTable
                        columns={columns}
                        data={filteredMaintenanceChecks}
                        pageSize={20}
                        onChange={handleFilterOnChange}
                    />
                ) : (
                    <List />
                )}
            </div>
        </>
    )
}
