import { VESSEL_STATUS } from '@/app/lib/graphQL/query'
import {
    SealogsCrewIcon,
    SealogsDocumentLockerIcon,
    SealogsLogbookIcon,
    SealogsMaintenanceIcon,
    SealogsTrainingIcon,
} from '@/app/lib/icons'
import { useToast } from '@/hooks/use-toast'
import { useState } from 'react'
import VesselsStatusEdit from '../status-edit-popover'
import { LogbookActionMenu } from '../../logbook/components/logbook-dropdown'
import { useRouter } from 'next/navigation'
import { MoreHorizontal } from 'lucide-react'

interface IProps {
    vessel: any
    onChangeStatusSuccess?: (vesselStatus: any) => void
}

export const TableActionColumn = ({
    vessel,
    onChangeStatusSuccess,
}: IProps) => {
    const [displayEditStatus, setDisplayEditStatus] = useState(false)
    const router = useRouter()
    const { toast } = useToast()

    const handleChangeStatus = () => {
        if (vessel.logentryID > 0) {
            toast({
                variant: 'destructive',
                description:
                    'There is an Open LogBook entry, Please complete the entry in order to update the vessel status',
            })

            return
        }

        setDisplayEditStatus(true)
    }

    // Create menu items for the LogbookActionMenu
    const menuItems = [
        {
            label: 'Maintenance',
            value: 'maintenance',
            icon: <SealogsMaintenanceIcon className="icons h-6 w-6" />,
            url: `/vessel/info?id=${vessel.id}&tab=maintenance`,
        },
        {
            label: 'Crew',
            value: 'crew',
            icon: <SealogsCrewIcon className="icons h-6 w-6" />,
            url: `/vessel/info?id=${vessel.id}&tab=crew`,
        },
        {
            label: 'Training & drills',
            value: 'crew_training',
            icon: <SealogsTrainingIcon className="icons h-6 w-6" />,
            url: `/vessel/info?id=${vessel.id}&tab=crew_training`,
        },
        {
            label:
                vessel.logentryID !== 0
                    ? 'Open Logbook entry'
                    : 'Logbook entry',
            value: vessel.logentryID !== 0 ? 'logbook-open' : 'logbook',
            icon: <SealogsLogbookIcon className="icons h-6 w-6" />,
            url:
                vessel.logentryID !== 0
                    ? `/log-entries?vesselID=${vessel.id}&logentryID=${vessel.logentryID}`
                    : `/vessel/info?id=${vessel.id}`,
        },
        {
            label: 'Documents',
            value: 'documents',
            icon: <SealogsDocumentLockerIcon className="icons h-6 w-6" />,
            url: `/vessel/info?id=${vessel.id}&tab=documents`,
        },
    ]

    return (
        <>
            <LogbookActionMenu
                backLabel="View vessel"
                triggerIcon={<MoreHorizontal className="h-4 w-4" />}
                onBack={() => router.push(`/vessel/info?id=${vessel.id}`)}
                items={menuItems}
                onDistructAction={() => handleChangeStatus()}
                ShowDistructive={true}
                disTructLabel={
                    vessel.status && vessel.status.status !== 'OutOfService'
                        ? 'Take vessel out of service'
                        : 'Change vessel status'
                }
            />
            <VesselsStatusEdit
                vessel={vessel}
                display={displayEditStatus}
                setDisplay={setDisplayEditStatus}
                onChangeStatusSuccess={onChangeStatusSuccess}
            />
        </>
    )
}
