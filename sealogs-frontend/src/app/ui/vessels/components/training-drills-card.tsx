'use client'
import { useState, useRef } from 'react'
import { SealogsTrainingIcon } from '../../../lib/icons/SealogsTrainingIcon'
import { SealogsTraining } from '@/app/lib/icons/SealogsTraining'
import { DataTable, ExtendedColumnDef } from '@/components/filteredTable'
import { H1, H2 } from '@/components/ui/typography'
import Link from 'next/link'
import { useIsMobile } from '@/components/hooks/use-mobile'
import { Button } from '@/components/ui/button'

interface TrainingDue {
    id: string
    title: string
    due: string
    members?: {
        firstName: string
        surname: string
    }[]
}

export default function TrainingDrillsCard({
    trainingSessionDuesSummary,
    setVesselTab,
    scrollToTabs,
}: {
    trainingSessionDuesSummary: TrainingDue[]
    setVesselTab: (tab: string) => void
    scrollToTabs?: () => void
}) {
    const isMobile = useIsMobile()
    const initialPageSize = 5 // Initial number of training sessions to show
    const incrementPageSize = 20 // Number of training sessions to add when "View More" is clicked
    const [visiblePages, setVisiblePages] = useState(initialPageSize)
    const cardRef = useRef<HTMLDivElement>(null)

    // Calculate how many training sessions to show based on visible pages
    const entriesToShow = visiblePages
    const trainingToDisplay = isMobile
        ? trainingSessionDuesSummary?.slice(0, entriesToShow) || []
        : trainingSessionDuesSummary?.slice(0, 5) || []
    const hasMoreTraining =
        trainingSessionDuesSummary &&
        trainingSessionDuesSummary.length > entriesToShow
    const columns = [
        {
            accessorKey: 'title',
            header: '',
            cell: ({ row }: { row: any }) => {
                const due = row.original
                return (
                    <div className="flex items-center h-full">
                        <div className="flex flex-col hover:text-curious-blue-800">
                            <span key={due.trainingType.id}>
                                {due.trainingType.title}
                            </span>
                        </div>
                    </div>
                )
            },
        },
        {
            accessorKey: 'due',
            header: '',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const due = row.original
                const dueDate = new Date(due.dueDate)
                const today = new Date()

                dueDate.setHours(0, 0, 0, 0)
                today.setHours(0, 0, 0, 0)

                const diffTime = today.getTime() - dueDate.getTime()
                const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

                return (
                    <div className="flex justify-between items-center text-nowrap">
                        {diffDays > 0 ? (
                            <div className="alert">
                                {diffDays + ' '}
                                days ago
                            </div>
                        ) : (
                            <div className="p-2 text-foreground">
                                Due -{' ' + diffDays * -1 + ' '}
                                days
                            </div>
                        )}
                    </div>
                )
            },
        },
    ]
    return (
        <>
            <div
                ref={cardRef}
                className="flex py-3 items-baseline gap-2 phablet:gap-4">
                <SealogsTrainingIcon
                    className={`h-12 w-12 ring-1 p-1 rounded-full`}
                />
                <Link href={`/vessel`}>
                    <H1>Training / drills</H1>
                </Link>
            </div>
            {trainingSessionDuesSummary &&
            trainingSessionDuesSummary.length > 0 ? (
                <>
                    <div className="pt-0 phablet:pt-1">
                        <DataTable
                            columns={
                                columns as ExtendedColumnDef<any, unknown>[]
                            }
                            data={trainingToDisplay}
                            showToolbar={false}
                            pageSize={
                                isMobile
                                    ? trainingToDisplay.length
                                    : initialPageSize
                            }
                            className="border-0 shadow-none"
                        />
                    </div>

                    {trainingSessionDuesSummary.length > initialPageSize && (
                        <div className="mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-curious-blue-50 border border-curious-blue-100 p-5 text-center">
                            {isMobile && hasMoreTraining ? (
                                <Button
                                    variant="outline"
                                    onClick={() =>
                                        setVisiblePages(
                                            (prev) => prev + incrementPageSize,
                                        )
                                    }
                                    className="text-accent uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto">
                                    View More
                                    <span className="hidden md:inline-block">
                                        &nbsp;crew training&nbsp;
                                    </span>
                                </Button>
                            ) : isMobile &&
                              !hasMoreTraining &&
                              trainingSessionDuesSummary.length >
                                  initialPageSize ? (
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setVisiblePages(initialPageSize)
                                        cardRef.current?.scrollIntoView({
                                            behavior: 'smooth',
                                            block: 'start',
                                        })
                                    }}
                                    className="text-accent uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto">
                                    View Less
                                    <span className="hidden md:inline-block">
                                        &nbsp;crew training&nbsp;
                                    </span>
                                </Button>
                            ) : (
                                <Link
                                    href="#"
                                    onClick={(e) => {
                                        e.preventDefault()
                                        setVesselTab('crew_training')
                                        scrollToTabs?.()
                                    }}
                                    className="hidden md:block text-accent uppercase hover:text-primary text-xs">
                                    View all
                                    <span className="hidden md:inline-block">
                                        &nbsp;crew training&nbsp;
                                    </span>
                                </Link>
                            )}
                        </div>
                    )}
                </>
            ) : (
                <div className="flex justify-between items-center gap-2 p-2 pt-4">
                    <div>
                        <SealogsTraining />
                    </div>
                    <p className="  ">
                        WOW! Look at that. All your crew are ship-shaped and
                        trained to the gills. Great job, captain!
                    </p>
                </div>
            )}
        </>
    )
}
