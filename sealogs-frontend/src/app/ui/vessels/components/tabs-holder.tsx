'use client'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import InfoTab from './tabs/info'
import MaintenanceTab from './tabs/maintenance'
import LogbookTabs from './tabs/logbook'
import CrewTab from './tabs/crew'
import CrewTrainingTab from './tabs/crew-training'
import InventoryTab from './tabs/inventory'
import DocumentsTab from './tabs/documents'
import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect } from 'react'
import { Badge } from '@/components/ui'

export default function TabsHolder({
    taskCounter,
    trainingDueCounter,
    vessel,
    engineList,
    fuelTankList,
    waterTankList,
    sewageSystemList,
    vesselId,
    logbooks,
    totalEntries,
    perPage,
    handlePagination,
    currentPage,
    maintenanceTasks,
    crewInfo,
    trainingSessions,
    trainingSessionDues,
    inventories,
    imCrew,
    edit_docs,
    setDocuments,
    documents,
    delete_docs,
    deleteFile,
    setVesselTab,
    addButton,
    vesselTab,
}: {
    taskCounter: number
    trainingDueCounter: number
    vessel: any
    engineList: any
    fuelTankList: any
    waterTankList: any
    sewageSystemList: any
    vesselId: number
    logbooks: any
    totalEntries: any
    perPage: any
    handlePagination: any
    currentPage: any
    maintenanceTasks: any
    crewInfo: any
    trainingSessions: any
    trainingSessionDues: any
    inventories: any
    imCrew: any
    edit_docs: any
    setDocuments: any
    documents: any
    delete_docs: any
    deleteFile: any
    setVesselTab: any
    addButton?: any
    vesselTab: any
}) {
    const searchParams = useSearchParams()
    const defaultTab = searchParams.get('tab') || 'logEntries'

    useEffect(() => {
        setVesselTab(defaultTab)
    }, [defaultTab])
    return (
        <div className="my-4 w-full">
            <Tabs
                defaultValue={defaultTab}
                value={vesselTab}
                onValueChange={setVesselTab}>
                <TabsList>
                    <TabsTrigger value="info">Info</TabsTrigger>
                    <TabsTrigger value="logEntries">Log Entries</TabsTrigger>
                    <TabsTrigger value="maintenance">
                        Maintenance
                        {taskCounter > 0 && (
                            <Badge
                                className="size-6 ml-2.5"
                                variant="destructive">
                                {taskCounter}
                            </Badge>
                        )}
                    </TabsTrigger>
                    <TabsTrigger value="crew">Crew</TabsTrigger>
                    <TabsTrigger value="crew_training">
                        Crew Training{' '}
                        {trainingDueCounter > 0 && (
                            <Badge
                                className="size-6 ml-2.5"
                                variant="destructive">
                                {trainingDueCounter}
                            </Badge>
                        )}
                    </TabsTrigger>
                    <TabsTrigger value="inventory">Inventory</TabsTrigger>
                    <TabsTrigger value="documents">Documents</TabsTrigger>
                </TabsList>
                {/* {addButton} */}
                <TabsContent value="info" className='bg-background rounded-lg'>
                    <InfoTab
                        vessel={vessel}
                        engineList={engineList}
                        fuelTankList={fuelTankList}
                        waterTankList={waterTankList}
                        sewageSystemList={sewageSystemList}
                    />
                </TabsContent>
                <TabsContent value="logEntries">
                    <LogbookTabs
                        vesselId={vesselId}
                        logbooks={logbooks}
                        totalEntries={totalEntries}
                        perPage={perPage}
                        handlePagination={handlePagination}
                        currentPage={currentPage}
                    />
                </TabsContent>
                <TabsContent value="maintenance">
                    <MaintenanceTab
                        maintenanceTasks={maintenanceTasks}
                        crewInfo={crewInfo}
                    />
                </TabsContent>
                <TabsContent value="crew">
                    <CrewTab crewInfo={crewInfo} vessel={vessel} />
                </TabsContent>
                <TabsContent value="crew_training">
                    <CrewTrainingTab
                        trainingSessions={trainingSessions}
                        trainingSessionDues={trainingSessionDues}
                    />
                </TabsContent>
                <TabsContent value="inventory">
                    <InventoryTab inventories={inventories} />
                </TabsContent>
                <TabsContent value="documents">
                    <DocumentsTab
                        imCrew={imCrew}
                        edit_docs={edit_docs}
                        setDocuments={setDocuments}
                        documents={documents}
                        delete_docs={delete_docs}
                        deleteFile={deleteFile}
                    />
                </TabsContent>
            </Tabs>
        </div>
    )
}
