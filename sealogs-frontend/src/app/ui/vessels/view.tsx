'use client'

import { But<PERSON> } from '@/components/ui/button'
import {
    GET_LOGBOOK_ENTRY_BY_ID,
    GET_CREW_BY_IDS,
    GET_FUELTANKS,
    GET_ENGINES,
    GET_SEWAGESYSTEMS,
    GET_WATERTANKS,
    GET_FILES,
    GET_INVENTORY_BY_VESSEL_ID,
    GET_INVENTORIES,
} from '@/app/lib/graphQL/query'
import {
    UPDATE_VESSEL,
    CREATE_LOGBOOK,
    CREATE_LOGBOOK_ENTRY,
    CREATE_VESSELSTATUS,
} from '@/app/lib/graphQL/mutation'
import { useLazyQuery, useMutation } from '@apollo/client'
import { useEffect, useMemo, useState, useRef } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
    getVesselByID,
    getTrainingSessionsByVesselId,
    getComponentMaintenanceCheckByVesselId,
    getLogBookEntries,
    getTrainingSessionDuesByVesselId,
} from '@/app/lib/actions'
import { isEmpty } from 'lodash'
import dayjs from 'dayjs'
import CrewMultiSelectDropdown from '../crew/multiselect-dropdown'
import { useToast } from '@/hooks/use-toast'
import { useIsMobile } from '@/components/hooks/use-mobile'

import { getPermissions, hasPermission, isCrew } from '@/app/helpers/userHelper'
import { usePathname, useSearchParams } from 'next/navigation'
import {
    getTasksDueCount,
    getTrainingsDueCount,
} from '@/app/helpers/vesselHelper'
import HeaderImage from './components/header-image'
import LogBookEntriesCard from './components/logbook-entries-card'
import MaintenanceCard from './components/maintenance-card'
import TrainingDrillsCard from './components/training-drills-card'
import CrewCard from './components/crew-card'
import InventoryCard from './components/inventory-card'
import TabsHolder from './components/tabs-holder'
import LogBookEntryModel from '@/app/offline/models/logBookEntry'
import VesselModel from '@/app/offline/models/vessel'
import SeaLogsMemberModel from '@/app/offline/models/seaLogsMember'
import { mergeTrainingSessionDues } from '@/app/helpers/trainingHelper'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/app/lib/utils'
import { FooterWrapper } from '@/components/footer-wrapper'
import { ArrowLeft, PencilIcon } from 'lucide-react'
import {
    AlertDialogNew,
    Card,
    Combobox,
    Label,
    Textarea,
} from '@/components/ui'
import DatePicker from '@/components/DateRange'
import Editor from '../editor'

const vesselStatuses: any = [
    { label: 'On Voyage', value: 'OnVoyage' },
    { label: 'Ready For Voyage', value: 'AvailableForVoyage' },
    { label: 'Out Of Service', value: 'OutOfService' },
]
const vesselStatusReason: any = [
    { label: 'Crew Unavailable', value: 'CrewUnavailable' },
    { label: 'Skipper/Master Unavailable', value: 'MasterUnavailable' },
    { label: 'Planned Maintenance', value: 'PlannedMaintenance' },
    { label: 'Breakdown', value: 'Breakdown' },
    { label: 'Other', value: 'Other' },
]

export default function VesselsView({
    vesselId,
    tab,
}: {
    vesselId: number
    tab: any
}) {
    const pathname = usePathname()
    const searchParams = useSearchParams()
    const { toast } = useToast()
    const [vessel, setVessel] = useState<any>()
    const [logbooks, setLogbooks] = useState<any>([])
    const [currentLogEntryAction, setCurrentLogEntryAction] = useState<any>()
    const [currentLogEntry, setCurrentLogEntry] = useState<any>()
    const [totalEntries, setTotalEntries] = useState(0)
    const [currentPage, setCurrentPage] = useState(0)
    const [maintenanceTasks, setMaintenanceTasks] = useState<any>([])
    const [taskCounter, setTaskCounter] = useState(0)
    const [trainingSessions, setTrainingSessions] = useState<any>()
    const [trainingSessionDues, setTrainingSessionDues] = useState<any>([])
    const [trainingSessionDuesSummary, setTrainingSessionDuesSummary] =
        useState<any>([])
    const [crewInfo, setCrewInfo] = useState<any>()
    const [taskCrewInfo, setTaskCrewInfo] = useState<any>()
    const [inventories, setInventories] = useState<any>([])
    const [engineList, setEngineList] = useState<any>()
    const [fuelTankList, setFuelTankList] = useState<any>()
    const [waterTankList, setWaterTankList] = useState<any>()
    const [sewageSystemList, setSewageSystemList] = useState<any>()
    const [documents, setDocuments] = useState<Array<Record<string, any>>>([])
    const router = useRouter()
    const [vesselTab, setVesselTab] = useState(
        searchParams.get('tab') ?? 'logEntries',
    )
    const perPage = 10
    const [displayAddCrew, setDisplayAddCrew] = useState(false)
    const [vesselCrewIDs, setVesselCrewIDs] = useState<any>([])
    const [bannerImage, setBannerImage] = useState<any>(false)
    const [isNewLogEntryDisabled, setIsNewLogEntryDisabled] = useState(true)
    const [imCrew, setImCrew] = useState(false)
    const [permissions, setPermissions] = useState<any>(false)
    const [edit_logBookEntry, setEdit_logBookEntry] = useState<any>(false)
    const [edit_task, setEdit_task] = useState<any>(false)
    const [edit_docs, setEdit_docs] = useState<any>(false)
    const [delete_docs, setDelete_docs] = useState<any>(false)
    const [trainingsDueCount, setTrainingsDueCount] = useState(0)
    const [displayEditStatus, setDisplayEditStatus] = useState(false)
    const [vesselStatus, setVesselStatus] = useState<any>([])
    const isMobile = useIsMobile()
    const tabsRef = useRef<HTMLDivElement>(null)

    const lbeModel = new LogBookEntryModel()
    const vesselModel = new VesselModel()
    const seaLogsMemberModel = new SeaLogsMemberModel()
    const init_permissions = () => {
        if (permissions) {
            if (
                hasPermission('ADD_LOGBOOKENTRY', permissions) ||
                hasPermission('EDIT_LOGBOOKENTRY', permissions)
            ) {
                setEdit_logBookEntry(true)
            } else {
                setEdit_logBookEntry(false)
            }
            if (hasPermission('EDIT_TASK', permissions)) {
                setEdit_task(true)
            } else {
                setEdit_task(false)
            }
            if (hasPermission('EDIT_VESSEL_DOCUMENT', permissions)) {
                setEdit_docs(true)
            } else {
                setEdit_docs(false)
            }
            if (hasPermission('DELETE_VESSEL_DOCUMENT', permissions)) {
                setDelete_docs(true)
            } else {
                setDelete_docs(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    useEffect(() => {
        setVesselStatus(vessel?.statusHistory?.nodes[0])
        if (vessel?.statusHistory?.nodes?.length === 0) {
            createVesselStatus({
                variables: {
                    input: {
                        date: dayjs().format('YYYY-MM-DD'),
                        vesselID: +vessel?.id,
                    },
                },
            })
        }
    }, [vessel])

    const handleUpdateVesselStatus = () => {
        {
            vesselStatus?.status === 'OutOfService'
                ? createVesselStatus({
                      variables: {
                          input: {
                              vesselID: vessel?.id,
                              date: vesselStatus?.date,
                              status: vesselStatus?.status,
                              comment: vesselStatus?.comment,
                              reason: vesselStatus?.reason,
                              otherReason: vesselStatus?.otherReason,
                              expectedReturn: vesselStatus?.expectedReturn,
                          },
                      },
                  })
                : createVesselStatus({
                      variables: {
                          input: {
                              vesselID: vessel?.id,
                              date: vesselStatus?.date,
                              status: vesselStatus?.status,
                          },
                      },
                  })
        }
    }

    const handleVesselStatusDate = (date: any) => {
        setVesselStatus({
            ...vesselStatus,
            date: date,
        })
    }

    const handleVesselStatusReturnDate = (date: any) => {
        setVesselStatus({
            ...vesselStatus,
            expectedReturn: date,
        })
    }

    const [createVesselStatus] = useMutation(CREATE_VESSELSTATUS, {
        onCompleted: (response: any) => {
            const data = response.createLogBookEntry
            setVesselStatus({
                ...vesselStatus,
                vesselID: vessel?.id,
                date: vesselStatus?.date,
                status: vesselStatus?.status,
                comment: vesselStatus?.comment,
                reason: vesselStatus?.reason,
                otherReason: vesselStatus?.otherReason,
                expectedReturn: vesselStatus?.expectedReturn,
            })
            setDisplayEditStatus(false)
        },
        onError: (error: any) => {
            toast({
                title: 'Error',
                description: error.message,
                variant: 'destructive',
            })
        },
    })

    const handleVesselStatusChange = (value: any) => {
        if (!hasLogbookOpen) {
            value?.value === 'OnVoyage'
                ? toast({
                      title: 'Error',
                      description:
                          'There is no Open LogBook entry, Please create a Logbook entry to set the vessel on voyage',
                      variant: 'destructive',
                  })
                : setVesselStatus({
                      ...vesselStatus,
                      status: value?.value,
                  })
        } else {
            value.value !== 'OnVoyage'
                ? toast({
                      title: 'Error',
                      description:
                          'There is an Open LogBook entry, Please complete the entry in order to update the vessel status',
                      variant: 'destructive',
                  })
                : setVesselStatus({
                      ...vesselStatus,
                      status: value?.value,
                  })
        }
    }

    const handleVesselStatusReasonChange = (value: any) => {
        setVesselStatus({
            ...vesselStatus,
            reason: value?.value,
        })
    }

    const handleSetLogbooks = (data: any) => {
        data.sort((a: any, b: any) =>
            b.state === 'Locked' ? -1 : a.state === 'Locked' ? 1 : 0,
        )
        let lbs = [
            ...data
                .filter((entry: any) => entry.state !== 'Locked')
                .sort(
                    (a: any, b: any) =>
                        new Date(a.startDate).getTime() -
                        new Date(b.startDate).getTime(),
                ),
            ...data
                .filter((entry: any) => entry.state === 'Locked')
                .sort(
                    (a: any, b: any) =>
                        new Date(b.startDate).getTime() -
                        new Date(a.startDate).getTime(),
                ),
        ]

        console.info('Set Logbooks', data)

        setLogbooks(lbs)
        setIsNewLogEntryDisabled(false)
        {
            data.filter((entry: any) => entry.state !== 'Locked').length > 0 &&
                setCurrentLogEntryAction(
                    data.filter((entry: any) => entry.state !== 'Locked')[0],
                )
        }
        setTotalEntries(data.length)
        {
            data.filter((entry: any) => entry.state !== 'Locked').length > 0 &&
                loadLogEntry(
                    data.filter((entry: any) => entry.state !== 'Locked')[0].id,
                )
        }
    }

    getLogBookEntries(vesselId, handleSetLogbooks)

    // getInventoryByVesselId(vesselId, setInventories)

    const [queryInventoriesByVessel] = useLazyQuery(GET_INVENTORIES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readInventories.nodes
            if (data) {
                setInventories(data)
            }
        },
        onError: (error: any) => {
            console.error('queryInventories error', error)
        },
    })
    useEffect(() => {
        loadInventories()
    }, [])
    const loadInventories = async () => {
        await queryInventoriesByVessel({
            variables: {
                filter: { vesselID: { eq: +vesselId } },
            },
        })
    }

    const handleSetTrainingSessionDues = (data: any) => {
        const dues = mergeTrainingSessionDues(data).slice(0, 5)
        setTrainingsDueCount(getTrainingsDueCount(vesselId))
        setTrainingSessionDues(data)
        setTrainingSessionDuesSummary(dues)
    }
    getTrainingSessionDuesByVesselId(vesselId, handleSetTrainingSessionDues)

    getTrainingSessionsByVesselId(vesselId, setTrainingSessions)

    const [queryGetEngines] = useLazyQuery(GET_ENGINES, {
        // fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readEngines.nodes
            setEngineList(data)
        },
        onError: (error: any) => {
            console.error('getEngines error', error)
        },
    })

    const getEngines = async (engineIds: any) => {
        await queryGetEngines({
            variables: {
                id: engineIds,
            },
        })
    }

    const [queryGetFuelTanks] = useLazyQuery(GET_FUELTANKS, {
        // fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readFuelTanks.nodes
            setFuelTankList(data)
        },
        onError: (error: any) => {
            console.error('getFuelTanks error', error)
        },
    })

    const getFuelTanks = async (fuelTankIds: any) => {
        await queryGetFuelTanks({
            variables: {
                id: fuelTankIds,
            },
        })
    }

    const [queryGetWaterTanks] = useLazyQuery(GET_WATERTANKS, {
        // fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readWaterTanks.nodes
            setWaterTankList(data)
        },
        onError: (error: any) => {
            console.error('getWaterTanks error', error)
        },
    })

    const getWaterTanks = async (waterTankIds: any) => {
        await queryGetWaterTanks({
            variables: {
                id: waterTankIds,
            },
        })
    }

    const [queryGetSewageSystems] = useLazyQuery(GET_SEWAGESYSTEMS, {
        // fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.readSewageSystems.nodes
            setSewageSystemList(data)
        },
        onError: (error: any) => {
            console.error('getSewageSystems error', error)
        },
    })

    const getSewageSystems = async (sewageSystemIds: any) => {
        await queryGetSewageSystems({
            variables: {
                id: sewageSystemIds,
            },
        })
    }

    const handleSetVessel = (vessel: any) => {
        setVessel(vessel)
        setVesselCrewIDs(
            vessel?.seaLogsMembers?.nodes.map((crew: any) => crew.id),
        )
        vessel?.seaLogsMembers &&
            loadCrewMemberInfo(
                vessel.seaLogsMembers.nodes
                    .filter((crew: any) => !crew.archived)
                    .map((crew: any) => +crew.id),
            )

        const engineIds = vessel?.parentComponent_Components?.nodes
            .filter(
                (item: any) =>
                    item.basicComponent.componentCategory === 'Engine',
            )
            .map((item: any) => {
                return item.basicComponent.id
            })
        const fuelTankIds = vessel?.parentComponent_Components?.nodes
            .filter(
                (item: any) =>
                    item.basicComponent.componentCategory === 'FuelTank',
            )
            .map((item: any) => {
                return item.basicComponent.id
            })
        const waterTankIds = vessel?.parentComponent_Components?.nodes
            .filter(
                (item: any) =>
                    item.basicComponent.componentCategory === 'WaterTank',
            )
            .map((item: any) => {
                return item.basicComponent.id
            })
        const sewageSystemIds = vessel?.parentComponent_Components?.nodes
            .filter(
                (item: any) =>
                    item.basicComponent.componentCategory === 'SewageSystem',
            )
            .map((item: any) => {
                return item.basicComponent.id
            })
        engineIds?.length > 0 && getEngines(engineIds)
        fuelTankIds?.length > 0 && getFuelTanks(fuelTankIds)
        waterTankIds?.length > 0 && getWaterTanks(waterTankIds)
        sewageSystemIds?.length > 0 && getSewageSystems(sewageSystemIds)
        vessel?.documents?.nodes?.length > 0 &&
            setDocuments(vessel.documents.nodes)

        if (vessel?.logBookID == 0) {
            createNewLogBook(vessel)
        }
        if (vessel?.bannerImageID !== '0' && vessel?.bannerImageID) {
            getFileDetails({
                variables: {
                    id: [vessel.bannerImageID],
                },
            })
        }
    }
    const [getFileDetails, { data, loading, error }] = useLazyQuery(GET_FILES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readFiles.nodes
            setBannerImage(process.env.FILE_BASE_URL + data[0]?.fileFilename)
        },
        onError: (error) => {
            console.error(error)
        },
    })
    const createNewLogBook = async (vessel: any) => {
        await createLogBook({
            variables: {
                input: {
                    title: vessel.title,
                },
            },
        })
    }

    const [createLogBook] = useMutation(CREATE_LOGBOOK, {
        onCompleted: (response: any) => {
            updateVessel({
                variables: {
                    input: {
                        id: vesselId,
                        logBookID: response.createLogBook.id,
                    },
                },
            })
        },
        onError: (error: any) => {
            console.error('createLogBook error', error)
        },
    })

    getVesselByID(vesselId, handleSetVessel)

    const handleSetMaintenanceTasks = (data: any) => {
        if (data.length === 0) {
            setMaintenanceTasks(false)
        }
        const tasks = data.filter((task: any) => {
            return task?.archived !== 1
        })
        const inventoryTasks = inventories.flatMap((inventory: any) => {
            const checks = inventory.componentMaintenanceChecks || []
            return checks
        })

        const combinedTasks = [...tasks, ...inventoryTasks]
        const seenIds = new Set()

        const deduplicatedTasks = combinedTasks.filter((task: any) => {
            const isDuplicate = seenIds.has(task.id)
            seenIds.add(task.id)
            return !isDuplicate
        })
        deduplicatedTasks.sort((a: any, b: any) => {
            // Add null checks for isOverDue
            const aStatus = a.isOverDue?.status
            const bStatus = b.isOverDue?.status

            if (aStatus === 'High' && bStatus !== 'High') {
                return -1
            } else if (aStatus !== 'High' && bStatus === 'High') {
                return 1
            } else if (aStatus === 'Medium' && bStatus !== 'Medium') {
                return -1
            } else if (aStatus !== 'Medium' && bStatus === 'Medium') {
                return 1
            } else if (aStatus === 'Medium' && bStatus === 'Medium') {
                return dayjs(b.startDate).diff(a.startDate)
            } else if (aStatus === 'High' && bStatus === 'High') {
                const aDays = a.isOverDue?.days
                    ? parseInt(a.isOverDue.days.match(/(\d+)/)?.[0] || '0')
                    : 0
                const bDays = b.isOverDue?.days
                    ? parseInt(b.isOverDue.days.match(/(\d+)/)?.[0] || '0')
                    : 0
                return bDays - aDays
            } else {
                // rest of the sort logic remains the same
                if (a.isCompleted === '1' && b.isCompleted === '1') {
                    if (a.expires === 'NA' && b.expires !== 'NA') {
                        return 1
                    } else if (a.expires !== 'NA' && b.expires === 'NA') {
                        return -1
                    } else {
                        return (
                            new Date(b.expires).getTime() -
                            new Date(a.expires).getTime()
                        )
                    }
                } else if (a.isCompleted === '1') {
                    return 1
                } else if (b.isCompleted === '1') {
                    return -1
                } else {
                    return dayjs(a.expires).diff(b.expires)
                }
            }
        })
        setMaintenanceTasks(deduplicatedTasks)
        // setMaintenanceTasks(
        //     deduplicatedTasks.filter((task: any) => {
        //         return task?.archived !== 1
        //     }),
        // )

        const appendedData: number[] = Array.from(
            new Set(
                deduplicatedTasks
                    .filter((task: any) => task.assignedToID > 0)
                    .map((task: any) => task.assignedToID),
            ),
        )
        loadCrewMemberInfo(appendedData, true)

        // const tomorrow = new Date()
        // tomorrow.setDate(tomorrow.getDate() + 1)

        // const taskCounter = deduplicatedTasks.filter(
        //     (task: any) =>
        //         task.status !== 'Completed' &&
        //         task.status !== 'Save_As_Draft' &&
        //         task.isOverDue?.ignore !== true,
        // ).length
        const taskCounter = getTasksDueCount(vesselId)
        setTaskCounter(taskCounter)
    }

    getComponentMaintenanceCheckByVesselId(vesselId, handleSetMaintenanceTasks)

    useEffect(() => {
        handleSetMaintenanceTasks(maintenanceTasks)
    }, [inventories])

    const loadLogEntry = async (logEntryId: number) => {
        await queryLogEntry({
            variables: {
                logbookEntryId: logEntryId,
            },
        })
    }

    const loadCrewMemberInfo = async (crewIds: number[], task = false) => {
        if (crewIds.length > 0) {
            task
                ? await queryTaskMembersInfo({
                      variables: {
                          crewMemberIDs: crewIds,
                      },
                  })
                : await queryCrewMemberInfo({
                      variables: {
                          crewMemberIDs: crewIds,
                      },
                  })
        }
    }

    const [queryLogEntry] = useLazyQuery(GET_LOGBOOK_ENTRY_BY_ID, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneLogBookEntry
            if (data) {
                setCurrentLogEntry(data)
            }
        },
    })

    const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readSeaLogsMembers.nodes
            if (data) {
                setCrewInfo(data)
            }
        },
        onError: (error: any) => {
            console.error('queryCrewMemberInfo error', error)
        },
    })

    const [queryTaskMembersInfo] = useLazyQuery(GET_CREW_BY_IDS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readSeaLogsMembers.nodes
            if (data) {
                setTaskCrewInfo(data)
            }
        },
        onError: (error: any) => {
            console.error('queryTaskMembersInfo error', error)
        },
    })

    const handlePagination = (page: number) => {
        if (page < 0 || currentPage === page) {
            return
        }
        setCurrentPage(page)
    }

    const deleteFile = (fileId: number) => {
        const newDocuments = documents.filter((doc: any) => doc.id !== fileId)
        setDocuments(newDocuments)
        updateVessel({
            variables: {
                input: {
                    id: vesselId,
                    documents: newDocuments.map((doc: any) => doc.id).join(','),
                },
            },
        })
    }

    const deleteAllFiles = (document: any) => {
        const newDocuments: any = []
        setDocuments(newDocuments)
        updateVessel({
            variables: {
                input: {
                    id: vesselId,
                    documents: newDocuments.map((doc: any) => doc.id).join(','),
                },
            },
        })
    }

    const [updateVessel] = useMutation(UPDATE_VESSEL, {
        onError: (error: any) => {
            console.error('updateVessel error', error)
        },
    })

    const handleUpdateVesselCrew = async () => {
        await updateVessel({
            variables: {
                input: {
                    id: vesselId,
                    seaLogsMembers: vesselCrewIDs.join(','),
                },
            },
        })
        setDisplayAddCrew(false)
        loadCrewMemberInfo(vesselCrewIDs)
    }
    const handleOnChangeVesselCrew = (data: any) => {
        setVesselCrewIDs(data.map((item: any) => item.value))
    }
    useEffect(() => {
        if (vesselTab === 'documents') {
            updateVessel({
                variables: {
                    input: {
                        id: vesselId,
                        documents: documents
                            .map((doc: any) => doc.id)
                            .join(','),
                    },
                },
            })
        }
        if (!isEmpty(tab)) {
            setVesselTab(tab)
        }
    }, [documents, tab])

    const handleCreateNewLogEntry = async () => {
        if (!edit_logBookEntry) {
            toast({
                title: 'Error',
                description:
                    'You do not have permission to create a new log entry',
                variant: 'destructive',
            })
            return
        }
        if (hasLogbookOpen) {
            toast({
                title: 'Error',
                description: (
                    <div>
                        Please complete the open log entry{' '}
                        <Link
                            href={`/log-entries?vesselID=${vesselId}&logentryID=${
                                logbooks.filter(
                                    (entry: any) => entry.state !== 'Locked',
                                )[0].id
                            }`}>
                            <span className="underline">here</span>
                        </Link>{' '}
                        before creating a new one.
                    </div>
                ),
                variant: 'destructive',
            })
        } else if (vessel?.logBookID > 0) {
            setIsNewLogEntryDisabled(true)
            await createLogEntry({
                variables: {
                    input: {
                        logBookID: vessel.logBookID,
                        vehicleID: vesselId,
                    },
                },
            })
        }
    }

    const [createLogEntry] = useMutation(CREATE_LOGBOOK_ENTRY, {
        onCompleted: (response: any) => {
            router.push(
                `/log-entries?vesselID=${vesselId}&logentryID=${response.createLogBookEntry.id}`,
            )
        },
        onError: (error: any) => {
            console.error('createLogEntry error', error)
            toast({
                title: 'Error',
                description: error.message,
                variant: 'destructive',
            })
        },
    })

    const hasLogbookOpen = useMemo(() => {
        return (
            logbooks.filter((entry: any) => entry.state !== 'Locked').length > 0
        )
    }, [logbooks])

    const vesselStatusLabel = useMemo(() => {
        if (vesselStatus?.status === 'OutOfService') {
            return 'Out Of Service'
        }

        return hasLogbookOpen ? 'On Voyage' : 'Ready for Voyage'
    }, [hasLogbookOpen, vesselStatus])

    useEffect(() => {
        setImCrew(isCrew() || false)
    }, [])

    const scrollToTabs = () => {
        if (tabsRef.current) {
            tabsRef.current.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
            })
        }
    }

    const addButton = (
        <div className="hidden md:flex flex-row justify-end">
            {hasLogbookOpen ? (
                <div className="invisible"></div>
            ) : (
                <>
                    {vesselTab === 'logEntries' && (
                        <div>
                            {!imCrew && (
                                /*<SeaLogsButton
                                    text="New log entry"
                                    type="secondary"
                                    icon="new_logbook"
                                    color="slblue"
                                    action={handleCreateNewLogEntry}
                                    isDisabled={isNewLogEntryDisabled}
                                />*/
                                <Button
                                    disabled={isNewLogEntryDisabled}
                                    onClick={handleCreateNewLogEntry}>
                                    New log entry
                                </Button>
                            )}
                        </div>
                    )}
                </>
            )}
            {vesselTab === 'crew' && !imCrew && (
                <Button
                    onClick={() => {
                        setVesselCrewIDs(
                            vessel?.seaLogsMembers?.nodes.map(
                                (crew: any) => crew.id,
                            ),
                        )
                        setDisplayAddCrew(true)
                    }}>
                    Add crew
                </Button>
            )}
            {vesselTab === 'maintenance' && (
                /*<SeaLogsButton
                    text="Add task"
                    type="primary"
                    icon="check"
                    color="slblue"
                    // link={`/maintenance/new?vesselID=${vesselId}&redirect_to=${pathname}?${searchParams.toString()}%26tab=maintenance`}
                    action={() => {
                        router.push(
                            `/maintenance/new?vesselID=${vesselId}&redirect_to=${pathname}?${searchParams.toString()}%26tab=maintenance`,
                        )
                    }}
                />*/
                <Button
                    onClick={() => {
                        router.push(
                            `/maintenance/new?vesselID=${vesselId}&redirect_to=${pathname}?${searchParams.toString()}%26tab=maintenance`,
                        )
                    }}>
                    Add task
                </Button>
            )}
            {permissions && hasPermission('RECORD_TRAINING', permissions) && (
                <>
                    {vesselTab === 'crew_training' && (
                        /*<SeaLogsButton
                                text="Add Training"
                                type="primary"
                                icon="check"
                                color="slblue"
                                // link={`/crew-training/create?vesselID=${vesselId}`}
                                action={() => {
                                    router.push(
                                        `/crew-training/create?vesselID=${vesselId}`,
                                    )
                                }}
                            />*/
                        <Button
                            onClick={() => {
                                router.push(
                                    `/crew-training/create?vesselID=${vesselId}`,
                                )
                            }}>
                            Add Training
                        </Button>
                    )}
                </>
            )}
            {vesselTab === 'inventory' && !imCrew && (
                /*<SeaLogsButton
                    text="Add Inventory"
                    type="primary"
                    icon="check"
                    color="slblue"
                    // link={`/inventory/new?vesselID=${vesselId}&redirectTo=${pathname}?${searchParams.toString()}%26tab=inventory`}
                    action={() => {
                        router.push(
                            `/inventory/new?vesselID=${vesselId}&redirectTo=${pathname}?${searchParams.toString()}%26tab=inventory`,
                        )
                    }}
                />*/
                <Button
                    onClick={() => {
                        router.push(
                            `/inventory/new?vesselID=${vesselId}&redirectTo=${pathname}?${searchParams.toString()}%26tab=inventory`,
                        )
                    }}>
                    Add Inventory
                </Button>
            )}
        </div>
    )

    // console.log('vessel', vessel)

    return (
        <>
            <HeaderImage bannerImage={bannerImage} />
            <div className="w-full flex flex-col gap-8 overflow-hidden relative px-1 phablet:px-4 -mt-20 md:-mt-24">
                <div className="grid grid-col-1 lg:grid-cols-3 gap-8 lg:gap-6 xl:gap-8">
                    <Card>
                        <LogBookEntriesCard
                            vesselId={vesselId}
                            logbooks={logbooks}
                            imCrew={imCrew}
                            handleCreateNewLogEntry={handleCreateNewLogEntry}
                            isNewLogEntryDisabled={isNewLogEntryDisabled}
                            setVesselTab={setVesselTab}
                            vesselTitle={vessel?.title}
                            scrollToTabs={scrollToTabs}
                        />
                    </Card>

                    <Card>
                        <MaintenanceCard
                            maintenanceTasks={maintenanceTasks}
                            pathname={pathname}
                            setVesselTab={setVesselTab}
                            scrollToTabs={scrollToTabs}
                        />
                    </Card>

                    <Card>
                        {permissions &&
                            hasPermission('VIEW_TRAINING', permissions) && (
                                <TrainingDrillsCard
                                    trainingSessionDuesSummary={
                                        trainingSessionDuesSummary
                                    }
                                    setVesselTab={setVesselTab}
                                    scrollToTabs={scrollToTabs}
                                />
                            )}
                    </Card>

                    {isMobile && (
                        <>
                            <Card>
                                <CrewCard
                                    crewInfo={crewInfo}
                                    setVesselTab={setVesselTab}
                                    vesselId={vesselId}
                                    pathname={pathname}
                                />
                            </Card>

                            <Card>
                                <InventoryCard
                                    inventories={inventories}
                                    setVesselTab={setVesselTab}
                                    vesselId={vesselId}
                                    pathname={pathname}
                                />
                            </Card>
                        </>
                    )}
                </div>
            </div>

            <div className="hidden md:block" ref={tabsRef}>
                {/* Navigation tabs */}
                <TabsHolder
                    taskCounter={taskCounter}
                    trainingDueCounter={trainingsDueCount}
                    vessel={vessel}
                    engineList={engineList}
                    fuelTankList={fuelTankList}
                    waterTankList={waterTankList}
                    sewageSystemList={sewageSystemList}
                    vesselId={vesselId}
                    logbooks={logbooks}
                    totalEntries={totalEntries}
                    perPage={perPage}
                    handlePagination={handlePagination}
                    currentPage={currentPage}
                    maintenanceTasks={maintenanceTasks}
                    crewInfo={crewInfo}
                    trainingSessions={trainingSessions}
                    trainingSessionDues={trainingSessionDues}
                    inventories={inventories}
                    imCrew={imCrew}
                    edit_docs={edit_docs}
                    setDocuments={setDocuments}
                    documents={documents}
                    delete_docs={delete_docs}
                    deleteFile={deleteFile}
                    setVesselTab={setVesselTab}
                    addButton={addButton}
                    vesselTab={vesselTab}
                />
            </div>
            <AlertDialogNew
                openDialog={displayAddCrew}
                setOpenDialog={setDisplayAddCrew}
                handleCreate={handleUpdateVesselCrew}
                title="Add Crew"
                actionText="Add Crew">
                {vessel && (
                    <CrewMultiSelectDropdown
                        value={vesselCrewIDs}
                        onChange={handleOnChangeVesselCrew}
                        departments={vessel?.departments?.nodes}
                    />
                )}
            </AlertDialogNew>
            <AlertDialogNew
                size="xl"
                openDialog={displayEditStatus}
                setOpenDialog={setDisplayEditStatus}
                handleCreate={handleUpdateVesselStatus}
                title="Update Vessel Status"
                actionText="Update"
                className="space-y-8">
                <DatePicker
                    mode="single"
                    onChange={handleVesselStatusDate}
                    placeholder="Select date"
                    value={vesselStatus?.date}
                />
                <div className="flex gap-2.5">
                    <Label label="Status" className="flex-1">
                        <Combobox
                            id="vessel-status"
                            options={vesselStatuses}
                            placeholder="Status"
                            value={vesselStatuses.find(
                                (status: any) =>
                                    vesselStatus?.status === status.value,
                            )}
                            onChange={handleVesselStatusChange}
                        />
                    </Label>
                    {vesselStatus?.status === 'OutOfService' && (
                        <Label
                            label="Reason for out of service"
                            className="flex-1">
                            <Combobox
                                id="vessel-status-reason"
                                options={vesselStatusReason}
                                placeholder="Reason"
                                value={vesselStatusReason.find(
                                    (status: any) =>
                                        vesselStatus?.reason === status.value,
                                )}
                                onChange={handleVesselStatusReasonChange}
                            />
                        </Label>
                    )}
                </div>

                {vesselStatus?.status === 'OutOfService' &&
                    vesselStatus?.reason === 'Other' && (
                        <Textarea
                            id="vessel-status-other"
                            placeholder="Other description"
                            value={vesselStatus?.otherReason}
                            onChange={(e) =>
                                setVesselStatus({
                                    ...vesselStatus,
                                    otherReason: e.target.value,
                                })
                            }
                        />
                    )}
                {vesselStatus?.status === 'OutOfService' && (
                    <Label label="Comments">
                        <Editor
                            id="comment"
                            placeholder="Comment"
                            className="bg-background"
                            content={vesselStatus?.comment}
                            handleEditorChange={(content: string) =>
                                setVesselStatus({
                                    ...vesselStatus,
                                    comment: content,
                                })
                            }
                        />
                    </Label>
                )}
                {vesselStatus?.status === 'OutOfService' && (
                    <div>
                        <Label label="Expected date of return" />
                        <DatePicker
                            className="flex w-full"
                            mode="single"
                            onChange={handleVesselStatusReturnDate}
                            placeholder="Select date"
                            value={vesselStatus?.expectedReturn}
                        />
                    </div>
                )}
            </AlertDialogNew>

            <FooterWrapper className=" justify-between">
                {vessel && vessel?.statusHistory?.nodes?.length > 0 && (
                    <div className="flex flex-wrap md:flex-inline items-center gap-2">
                        <strong>Status:</strong>
                        {vesselStatusLabel}
                        {/* {vesselStatus?.status?.split(/(?=[A-Z])/).join(' ')} */}
                        {!hasLogbookOpen && (
                            <Button
                                variant="ghost"
                                //iconLeft={PencilIcon}
                                onClick={() => setDisplayEditStatus(true)}>
                                Edit
                            </Button>
                        )}
                    </div>
                )}
                <div className="hidden md:flex flex-row gap-2">
                    <Button
                        variant="back"
                        onClick={() => router.back()}
                        iconLeft={ArrowLeft}>
                        Cancel
                    </Button>
                    {!imCrew && (
                        <DropdownMenu>
                            <DropdownMenuTrigger className="flex items-center">
                                <Button
                                    /*iconLeft={
                                        <svg
                                            key={'Edit'}
                                            className="-ml-0.5 mr-1.5 h-5 w-5 group-hover:border-white"
                                            viewBox="0 0 36 36"
                                            fill="currentColor"
                                            aria-hidden="true">
                                            <path d="M33.87,8.32,28,2.42a2.07,2.07,0,0,0-2.92,0L4.27,23.2l-1.9,8.2a2.06,2.06,0,0,0,2,2.5,2.14,2.14,0,0,0,.43,0L13.09,32,33.87,11.24A2.07,2.07,0,0,0,33.87,8.32ZM12.09,30.2,4.32,31.83l1.77-7.62L21.66,8.7l6,6ZM29,13.25l-6-6,3.48-3.46,5.9,6Z"></path>
                                        </svg>
                                    }*/
                                    variant="outline">
                                    Edit
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent
                                align="end"
                                className="w-[256px] p-0">
                                <div className="text-outer-space-500 flex flex-col items-left justify-left py-[9px]">
                                    <DropdownMenuItem
                                        className={cn(
                                            'flex flex-row w-full group relative m-0 py-0 gap-3 justify-start px-3.5 h-11 cursor-pointer focus:bg-gray-50 rounded-none',
                                        )}>
                                        {/* Circle indicator */}
                                        <div
                                            className={cn(
                                                'relative z-20 w-full',
                                            )}>
                                            <Button
                                                variant="ghost"
                                                className="w-full justify-start p-1 hover:bg-muted"
                                                onClick={() => {
                                                    router.push(
                                                        `/vessel/edit?id=${vesselId}`,
                                                    )
                                                }}>
                                                Edit Vessel
                                            </Button>
                                        </div>
                                        <div
                                            className={cn(
                                                'absolute w-full inset-0 mx-auto',
                                                'group-hover:bg-outer-space-50 group-hover:px-[6px] rounded-md group-hover:w-[233px] group-hover:border group-hover:border-border',
                                                'will-change-transform will-change-width will-change-padding transform-gpu',
                                                'transition-[width,padding] ease-out duration-600',
                                                'outline-none focus:outline-none active:outline-none',
                                            )}
                                        />
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                        className={cn(
                                            'flex flex-row w-full group relative m-0 py-0 gap-[11px] justify-start px-3.5 h-11 cursor-pointer focus:bg-curious-blue-50 rounded-none',
                                        )}>
                                        {/* Circle indicator */}
                                        <div
                                            className={cn(
                                                'relative z-20 w-full',
                                            )}>
                                            {vessel?.logBookID > 0 && (
                                                <Button
                                                    variant="ghost"
                                                    className="w-full justify-start p-1 hover:bg-wedgewood-50"
                                                    onClick={() => {
                                                        router.push(
                                                            `/vessel/logbook-configuration?logBookID=${vessel.logBookID}&vesselID=${vesselId}`,
                                                        )
                                                    }}>
                                                    Edit Logbook Configuration
                                                </Button>
                                            )}
                                        </div>
                                        <div
                                            className={cn(
                                                'absolute w-full inset-0 mx-auto',
                                                'group-hover:bg-curious-blue-50 group-hover:px-[6px] rounded-md group-hover:w-[233px] group-hover:border group-hover:border-border',
                                                'will-change-transform will-change-width will-change-padding transform-gpu',
                                                'transition-[width,padding] ease-out duration-600',
                                                'outline-none focus:outline-none active:outline-none',
                                            )}
                                        />
                                    </DropdownMenuItem>
                                </div>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    )}
                    {addButton}
                    {/*logbooks.filter(
                            (entry: any) => entry.state !== 'Locked',
                        ).length > 0 ? (
                            <div className="invisible"></div>
                        ) : (
                            <div>
                                {!imCrew && (
                                    <Button
                                        variant="secondary"
                                        disabled={isNewLogEntryDisabled}
                                        onClick={handleCreateNewLogEntry}
                                    >
                                        New log entry
                                    </Button>
                                )}
                            </div>
                        )}
                        {vesselTab === 'crew' && !imCrew && (
                            <Button
                                onClick={() => {
                                    setVesselCrewIDs(
                                        vessel?.seaLogsMembers?.nodes.map(
                                            (crew: any) => crew.id,
                                        ),
                                    )
                                    setDisplayAddCrew(true)
                                }}
                            >
                                Add crew
                            </Button>
                        )}
                        {vesselTab === 'maintenance' && (
                            <Button
                                onClick={() => {
                                    router.push(
                                        `/maintenance/new?vesselID=${vesselId}&redirect_to=${pathname}?${searchParams.toString()}%26tab=maintenance`,
                                    )
                                }}
                            >
                                Add task
                            </Button>
                        )}
                        {permissions &&
                            hasPermission('RECORD_TRAINING', permissions) && (
                                <>
                                    {vesselTab === 'crew_training' && (
                                        <Button
                                            onClick={() => {
                                                router.push(
                                                    `/crew-training/create?vesselID=${vesselId}`,
                                                )
                                            }}
                                        >
                                            Add Training
                                        </Button>
                                    )}
                                </>
                            )}
                        {vesselTab === 'inventory' && !imCrew && (
                            <Button
                                onClick={() => {
                                    router.push(
                                        `/inventory/new?vesselID=${vesselId}&redirectTo=${pathname}?${searchParams.toString()}%26tab=inventory`,
                                    )
                                }}
                            >
                                Add Inventory
                            </Button>
                        )*/}
                </div>
            </FooterWrapper>
        </>
    )
}
