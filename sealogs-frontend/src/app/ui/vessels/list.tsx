'use client'
import { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import Link from 'next/link'
import { DataTable } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import VesselPOB from './vessel-pob'
import VesselIcon from './vesel-icon'
import LocationMap from '@/components/location-map'
import { MapPin } from 'lucide-react'
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog'
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '@/components/ui/tooltip'
import { Badge } from '@/components/ui/badge'
import { getDashboardVesselList } from '@/app/lib/actions'
import { isEmpty, trim } from 'lodash'
import { useRouter } from 'next/navigation'
import { SealogsLocationIcon, SealogsVesselsIcon } from '@/app/lib/icons'
import { VesselsFilterActions } from '@/components/filter/components/vessels-actions'
import { ListHeader } from '@/components/ui/list-header'
import { TableActionColumn } from './components/table-action-column'
import { cn } from '@/app/lib/utils'

// Location Modal Component
const LocationModal = ({
    vessel,
    className,
    iconClassName,
}: {
    vessel: any
    className?: any
    iconClassName?: string
}) => {
    const hasLocation =
        vessel.vesselPosition &&
        (vessel.vesselPosition.geoLocation?.id > 0 ||
            (vessel.vesselPosition.lat && vessel.vesselPosition.lat))

    if (!hasLocation) return null

    return (
        <Dialog>
            <DialogTrigger
                onClick={(e) => e.stopPropagation()}
                className={cn(className)}>
                <SealogsLocationIcon className={cn('size-9', iconClassName)} />
            </DialogTrigger>
            <DialogContent className="max-w-6xl w-[95vw] p-0 overflow-hidden">
                <DialogHeader className="p-4 pb-0">
                    <DialogTitle className="flex items-center gap-2">
                        <MapPin size={18} />
                        {vessel.vesselPosition.geoLocation?.title}
                    </DialogTitle>
                </DialogHeader>
                <div className="h-[80vh] pt-0 grid">
                    <LocationMap
                        position={[
                            vessel.vesselPosition.lat ||
                                vessel.vesselPosition.geoLocation?.lat,
                            vessel.vesselPosition.long ||
                                vessel.vesselPosition.geoLocation?.long,
                        ]}
                        zoom={7}
                        vessel={vessel}
                    />
                </div>
            </DialogContent>
        </Dialog>
    )
}

export default function VesselsList(props: any) {
    const router = useRouter()
    const [vessels, setVessels] = useState<any>([])
    const [archiveVessels, setArchiveVessels] = useState<any>([])
    const [activeVessels, setActiveVessels] = useState<any>([])
    const [loading, setLoading] = useState<boolean>(true)
    const [filter, setFilter] = useState({} as SearchFilter)
    const [keywordFilter, setKeywordFilter] = useState([] as any)

    // Fetch vessels using your action; expect each vessel to have an "archived" property.
    const handleSetVessels = (fetchedVessels: any) => {
        const active = fetchedVessels.filter((v: any) => !v.archived)
        const archived = fetchedVessels.filter((v: any) => v.archived)
        setActiveVessels(active)
        setArchiveVessels(archived)
        // Show active vessels by default
        setVessels(active)
        setLoading(false)
    }

    // Call your action to fetch vessels.
    getDashboardVesselList(handleSetVessels, 2)

    const onChangeStatusSuccess = (vessel: any, status: any) => {
        setVessels((previousList: any) => {
            const dataIndex = previousList.findIndex(
                (item: any) => item.id == vessel.id,
            )

            if (dataIndex === -1) {
                return previousList
            }

            const newList = [...previousList]

            vessel.status = status

            newList[dataIndex] = vessel

            return newList
        })
    }

    // Column definitions for the DataTable.
    const columns = [
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Vessels" />
            ),
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                const vesselStatus = vessel.status

                return (
                    <div className="relative w-fit">
                        <Link
                            href={`/vessel/info?id=${vessel.id}&name=${vessel.title}`}>
                            {vesselStatus &&
                            vesselStatus.status !== 'OutOfService' ? (
                                <div className="flex flex-col tablet-md:flex-row gap-2 whitespace-nowrap items-center">
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <div className="border-2 relative border-bright-turquoise-600 rounded-full">
                                                    <VesselIcon
                                                        vessel={vessel}
                                                    />
                                                </div>
                                            </TooltipTrigger>
                                            <TooltipContent className="tablet-md:hidden">
                                                <p>{vessel.title}</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                    <div className="flex-col hidden tablet-md:flex">
                                        <div className="flex items-center">
                                            <span className="font-medium">
                                                {vessel.title}
                                            </span>
                                        </div>
                                        {vessel.logentryID !== 0 ? (
                                            <div className="text-accent text-[10px]">
                                                ON VOYAGE
                                            </div>
                                        ) : (
                                            <div className="text-accent text-[10px]">
                                                READY FOR VOYAGE
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ) : (
                                <div className="flex flex-col tablet-md:flex-row gap-2 whitespace-nowrap items-center ">
                                    <TooltipProvider>
                                        <Tooltip>
                                            <TooltipTrigger>
                                                <div className="relative size-fit">
                                                    <div className="relative inline-block border-2 min-w-fit border-cinnabar-600 opacity-50 rounded-full">
                                                        <VesselIcon
                                                            vessel={vessel}
                                                        />
                                                        <div className="absolute inset-0 bg-red-vivid-700 opacity-50 rounded-full">
                                                            {'        '}
                                                        </div>
                                                    </div>
                                                </div>
                                            </TooltipTrigger>
                                            <TooltipContent className="tablet-md:hidden">
                                                <p>{vessel.title}</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                    <div className="flex-col hidden tablet-md:flex">
                                        <div className="flex items-center">
                                            <span className="font-medium opacity-50">
                                                {vessel.title}
                                            </span>
                                        </div>
                                        <div className="inline-block text-[10px] text-destructive">
                                            OUT OF SERVICE
                                        </div>
                                    </div>
                                </div>
                            )}
                        </Link>
                        <LocationModal
                            className="absolute tablet-md:hidden -top-2.5 -right-4"
                            iconClassName="size-8"
                            vessel={vessel}
                        />
                    </div>
                )
            },
        },
        {
            accessorKey: 'pob',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="P.O.B" />
            ),
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                return <VesselPOB vessel={vessel} />
            },
        },
        {
            accessorKey: 'trainingsDue',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Training" />
            ),
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                return (
                    <>
                        {vessel.trainingsDue > 0 ? (
                            <Badge variant="destructive">
                                {vessel.trainingsDue}
                            </Badge>
                        ) : (
                            <Badge variant="success">
                                <svg
                                    className={`h-5 w-5`}
                                    viewBox="0 0 20 20"
                                    fill="#27AB83"
                                    aria-hidden="true">
                                    <path
                                        fillRule="evenodd"
                                        d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </Badge>
                        )}
                    </>
                )
            },
        },
        {
            accessorKey: 'tasksDue',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Tasks" />
            ),
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                return (
                    <>
                        {vessel.tasksDue > 0 ? (
                            <Badge variant="destructive">
                                {vessel.tasksDue}
                            </Badge>
                        ) : (
                            <Badge variant="success">
                                <svg
                                    className={`h-5 w-5`}
                                    viewBox="0 0 20 20"
                                    fill="#27AB83"
                                    aria-hidden="true">
                                    <path
                                        fillRule="evenodd"
                                        d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </Badge>
                        )}
                    </>
                )
            },
        },
        {
            accessorKey: 'postition',
            header: 'Location',
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                return (
                    <div className="hidden tablet-md:table-cell">
                        <LocationModal vessel={vessel} />
                    </div>
                )
            },
        },
        {
            id: 'actions',
            enableHiding: false,
            cell: ({ row }: { row: any }) => {
                const vessel = row.original
                return (
                    <TableActionColumn
                        vessel={vessel}
                        onChangeStatusSuccess={(newStatus) =>
                            onChangeStatusSuccess(vessel, newStatus)
                        }
                    />
                )
            },
        },
    ]

    const handleFilterOnChange = ({ type, data }: any) => {
        const searchFilter: SearchFilter = { ...filter }
        if (type === 'vessel') {
            if (data) {
                searchFilter.basicComponentID = { eq: +data.value }
            } else {
                delete searchFilter.basicComponentID
            }
        }
        let keyFilter = keywordFilter
        if (type === 'keyword') {
            if (!isEmpty(trim(data.value))) {
                keyFilter = [
                    { name: { contains: data.value } },
                    { comments: { contains: data.value } },
                    { workOrderNumber: { contains: data.value } },
                ]
            } else {
                keyFilter = []
            }
        }
        if (type === 'isArchived') {
            setVessels(!data ? archiveVessels : activeVessels)
        }
        setFilter(searchFilter)
        setKeywordFilter(keyFilter)
    }

    const handleDropdownChange = (type: string, data: any) => {
        handleFilterOnChange({ type, data })
    }

    return (
        <>
            <ListHeader
                icon={
                    <SealogsVesselsIcon
                        className={`h-12 w-12 ring-1 p-1 rounded-full`}
                    />
                }
                title="All vessels"
                actions={
                    <VesselsFilterActions
                        onChange={(data: any) => {
                            handleDropdownChange('isArchived', data)
                        }}
                    />
                }
            />
            <div className="mt-16">
                <DataTable
                    columns={columns}
                    data={vessels}
                    onChange={handleFilterOnChange}
                />
            </div>
        </>
    )
}
